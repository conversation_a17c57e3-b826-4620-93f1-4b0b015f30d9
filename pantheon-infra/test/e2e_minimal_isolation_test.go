package test

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/breezewish/pantheon-backend/internal/app"
	"github.com/breezewish/pantheon-backend/internal/config"
	"github.com/breezewish/pantheon-backend/internal/service"
	"github.com/go-resty/resty/v2"
	"github.com/stretchr/testify/require"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/mysql"
)

// TestMinimalIsolationE2E tests the minimal isolation scenario using the full server and HTTP API
func TestMinimalIsolationE2E(t *testing.T) {
	ctx := context.Background()

	// Start MySQL container
	mysqlContainer, err := mysql.Run(ctx,
		"mysql:8.0.36",
		mysql.WithDatabase("pantheon_test"),
		mysql.WithUsername("root"),
		mysql.WithPassword("root"),
	)
	require.NoError(t, err)
	t.Cleanup(func() {
		testcontainers.TerminateContainer(mysqlContainer)
	})

	// Get connection string
	connectionString, err := mysqlContainer.ConnectionString(ctx)
	require.NoError(t, err)

	// Setup test configuration
	tempDir := t.TempDir()
	cfg := &config.Config{
		HTTP: config.HTTPConfig{
			Port: 18080, // Use fixed port for testing
			Host: "127.0.0.1",
		},
		Database: config.DatabaseConfig{
			DSN: connectionString,
		},
		Storage: config.StorageConfig{
			DataDir: tempDir,
		},
	}

	// Ensure data directories exist
	require.NoError(t, cfg.EnsureDataDirs())

	// Create and start the application
	fxApp := app.NewFxApp(cfg)
	startCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	require.NoError(t, fxApp.Start(startCtx))
	t.Cleanup(func() {
		stopCtx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
		defer cancel()
		fxApp.Stop(stopCtx)
	})

	// Wait for server to start and be ready
	time.Sleep(1 * time.Second)

	// Create HTTP client
	client := resty.New()
	baseURL := fmt.Sprintf("http://%s:%d", cfg.HTTP.Host, cfg.HTTP.Port)
	client.SetBaseURL(baseURL)

	// Test health endpoint first
	resp, err := client.R().Get("/health")
	require.NoError(t, err)
	require.Equal(t, http.StatusOK, resp.StatusCode())

	// Import seed snap
	seedSnapReq := service.ImportSeedRequest{
		ID:          "mybox",
		DockerImage: "busybox:1.37.0",
		Envs:        map[string]string{},
	}

	seedResp, err := client.R().
		SetBody(seedSnapReq).
		Post("/api/v1/seed_snap/import")
	require.NoError(t, err)
	require.Equal(t, http.StatusCreated, seedResp.StatusCode())

	forestID := "test-forest"

	// Execute command to create a file in the base image (creates snapshot A)
	fmt.Println("Creating file in base image...")
	execReqA := service.ExecRequest{
		Command:     []string{"sh", "-c", "echo 'original' > /file.txt"},
		WorkingDir:  "/",
		NewSnapName: "snap-a",
		Envs:        map[string]string{},
	}

	execRespA, err := client.R().
		SetBody(execReqA).
		Post(fmt.Sprintf("/api/v1/forest/%s/snap/%s/exec", forestID, "mybox"))
	require.NoError(t, err)
	require.Equal(t, http.StatusOK, execRespA.StatusCode())

	var execResultA service.ExecResponse
	err = json.Unmarshal(execRespA.Body(), &execResultA)
	require.NoError(t, err)
	snapAID := execResultA.Snap.ID
	fmt.Printf("Created snapshot A: %s\n", snapAID)

	waitForSnapCompletion(t, client, forestID, snapAID)

	// Read file contents from snapshot A
	fmt.Println("Reading file from snapshot A...")
	readReqA := service.ExecRequest{
		Command:     []string{"sh", "-c", "cat /file.txt"},
		WorkingDir:  "/",
		NewSnapName: "read-a",
		Envs:        map[string]string{},
	}

	readRespA, err := client.R().
		SetBody(readReqA).
		Post(fmt.Sprintf("/api/v1/forest/%s/snap/%s/exec", forestID, snapAID))
	require.NoError(t, err)
	require.Equal(t, http.StatusOK, readRespA.StatusCode())

	var readResultA service.ExecResponse
	err = json.Unmarshal(readRespA.Body(), &readResultA)
	require.NoError(t, err)
	readSnapAID := readResultA.Snap.ID

	// Wait for read execution to complete
	waitForSnapCompletion(t, client, forestID, readSnapAID)

	// Get output from read operation
	outputRespA, err := client.R().
		Get(fmt.Sprintf("/api/v1/forest/%s/snap/%s/artifacts/output", forestID, readSnapAID))
	require.NoError(t, err)
	require.Equal(t, http.StatusOK, outputRespA.StatusCode())
	contentA := strings.TrimSpace(outputRespA.String())

	// Create a modified version in snapshot B
	fmt.Println("Creating modified file in snapshot B...")
	execReqB := service.ExecRequest{
		Command:     []string{"sh", "-c", "echo 'modified' > /file.txt"},
		WorkingDir:  "/",
		NewSnapName: "snap-b",
		Envs:        map[string]string{},
	}

	execRespB, err := client.R().
		SetBody(execReqB).
		Post(fmt.Sprintf("/api/v1/forest/%s/snap/%s/exec", forestID, snapAID))
	require.NoError(t, err)
	require.Equal(t, http.StatusOK, execRespB.StatusCode())

	var execResultB service.ExecResponse
	err = json.Unmarshal(execRespB.Body(), &execResultB)
	require.NoError(t, err)
	snapBID := execResultB.Snap.ID
	fmt.Printf("Created snapshot B: %s\n", snapBID)

	// Wait for execution to complete
	waitForSnapCompletion(t, client, forestID, snapBID)

	// Read file contents from snapshot B
	fmt.Println("Reading file from snapshot B...")
	readReqB := service.ExecRequest{
		Command:     []string{"sh", "-c", "cat /file.txt"},
		WorkingDir:  "/",
		NewSnapName: "read-b",
		Envs:        map[string]string{},
	}

	readRespB, err := client.R().
		SetBody(readReqB).
		Post(fmt.Sprintf("/api/v1/forest/%s/snap/%s/exec", forestID, snapBID))
	require.NoError(t, err)
	require.Equal(t, http.StatusOK, readRespB.StatusCode())

	var readResultB service.ExecResponse
	err = json.Unmarshal(readRespB.Body(), &readResultB)
	require.NoError(t, err)
	readSnapBID := readResultB.Snap.ID

	// Wait for read execution to complete
	waitForSnapCompletion(t, client, forestID, readSnapBID)

	// Get output from read operation
	outputRespB, err := client.R().
		Get(fmt.Sprintf("/api/v1/forest/%s/snap/%s/artifacts/output", forestID, readSnapBID))
	require.NoError(t, err)
	require.Equal(t, http.StatusOK, outputRespB.StatusCode())
	contentB := strings.TrimSpace(outputRespB.String())

	// Read file contents from snapshot A again to verify it's unchanged
	fmt.Println("Reading file from snapshot A again...")
	readReqA2 := service.ExecRequest{
		Command:     []string{"sh", "-c", "cat /file.txt"},
		WorkingDir:  "/",
		NewSnapName: "read-a2",
		Envs:        map[string]string{},
	}

	readRespA2, err := client.R().
		SetBody(readReqA2).
		Post(fmt.Sprintf("/api/v1/forest/%s/snap/%s/exec", forestID, snapAID))
	require.NoError(t, err)
	require.Equal(t, http.StatusOK, readRespA2.StatusCode())

	var readResultA2 service.ExecResponse
	err = json.Unmarshal(readRespA2.Body(), &readResultA2)
	require.NoError(t, err)
	readSnapA2ID := readResultA2.Snap.ID

	// Wait for read execution to complete
	waitForSnapCompletion(t, client, forestID, readSnapA2ID)

	// Get output from read operation
	outputRespA2, err := client.R().
		Get(fmt.Sprintf("/api/v1/forest/%s/snap/%s/artifacts/output", forestID, readSnapA2ID))
	require.NoError(t, err)
	require.Equal(t, http.StatusOK, outputRespA2.StatusCode())
	contentA2 := strings.TrimSpace(outputRespA2.String())

	// Compare and verify isolation
	fmt.Printf("Snapshot A (first read): '%s'\n", contentA)
	fmt.Printf("Snapshot B: '%s'\n", contentB)
	fmt.Printf("Snapshot A (second read): '%s'\n", contentA2)

	require.Equal(t, "original", contentA, "Snapshot A should have original content")
	require.Equal(t, "modified", contentB, "Snapshot B should have modified content")
	require.Equal(t, contentA, contentA2, "Snapshot A should remain unchanged")
	require.NotEqual(t, contentA, contentB, "Content should differ between snapshots")
}

// waitForSnapCompletion waits for a snap to finish execution
func waitForSnapCompletion(t *testing.T, client *resty.Client, forestID, snapID string) {
	timeout := time.After(30 * time.Second)
	ticker := time.NewTicker(500 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			t.Fatalf("Timeout waiting for snap %s to complete", snapID)
		case <-ticker.C:
			resp, err := client.R().
				Get(fmt.Sprintf("/api/v1/forest/%s/snap/%s", forestID, snapID))
			require.NoError(t, err)
			require.Equal(t, http.StatusOK, resp.StatusCode())

			var respObj service.GetSnapResponse
			err = json.Unmarshal(resp.Body(), &respObj)
			require.NoError(t, err)

			if respObj.Snap.FinishedAt.Valid {
				return // Snap is finished
			}
		}
	}
}
