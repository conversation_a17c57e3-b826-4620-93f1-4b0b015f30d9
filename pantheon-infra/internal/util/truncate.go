package util

import (
	"bufio"
	"bytes"
	"io"
	"os"
)

// Define the rules up front. Don't scatter magic numbers all over the code
// like a clueless first-year student.
const (
	headMaxLines = 300
	headMaxBytes = 50 * 1024 // 50 KB
	tailMaxLines = 500
	tailMaxBytes = 50 * 1024 // 50 KB

	truncationMarker = "<....truncated....>\n"
)

// TruncateFile preserves the head and tail of a file, replacing the middle
// with a marker. It writes the result to dstPath.
func TruncateFile(srcPath, dstPath string) error {
	src, err := os.Open(srcPath)
	if err != nil {
		return err
	}
	defer src.Close()

	info, err := src.Stat()
	if err != nil {
		return err
	}

	// Handle the trivial case where the file is too small to even consider
	// truncating. Just copy it. Anything else is a waste of CPU cycles.
	if info.Size() <= headMaxBytes+tailMaxBytes {
		return copyFile(srcPath, dstPath)
	}

	dst, err := os.Create(dstPath)
	if err != nil {
		return err
	}
	defer dst.Close()

	return processTruncation(src, dst, info.Size())
}

func processTruncation(src *os.File, dst io.Writer, size int64) error {
	writer := bufio.NewWriter(dst)
	defer writer.Flush()

	// --- Step 1: Handle the Head ---
	// We read up to headMaxBytes, but stop sooner if we hit headMaxLines.
	// io.LimitReader is perfect for this. It stops reading after N bytes.
	headReader := io.LimitReader(src, headMaxBytes)
	scanner := bufio.NewScanner(headReader)

	var linesWritten, headBytesWritten int
	for linesWritten < headMaxLines && scanner.Scan() {
		line := scanner.Bytes()
		n, err := writer.Write(line)
		if err != nil {
			return err
		}
		writer.WriteByte('\n')
		headBytesWritten += n + 1 // +1 for the newline
		linesWritten++
	}
	if err := scanner.Err(); err != nil {
		// This can happen with massive lines that bust the scanner's buffer.
		// That's fine. We've read our byte limit, which was one of the rules.
		// We just acknowledge it and move on.
	}

	// --- Step 2: Handle the Tail ---
	// This is the only tricky part. We find the starting point for the tail read.
	// It's either the total size minus our byte limit, or the end of the head,
	// whichever is *later*. This prevents overlap.
	tailReadStartOffset := size - tailMaxBytes
	if tailReadStartOffset < int64(headBytesWritten) {
		tailReadStartOffset = int64(headBytesWritten)
	}

	// If, after all that, the start of the tail is right after the head,
	// it means there's no gap. Just copy the rest of the file.
	if tailReadStartOffset <= int64(headBytesWritten) {
		// Rewind slightly to where the head reader left off.
		if _, err := src.Seek(int64(headBytesWritten), io.SeekStart); err != nil {
			return err
		}
		_, err := io.Copy(writer, src)
		return err
	}

	// --- Step 3: Write Marker and Tail ---
	// If we got here, there's a gap. So, write the marker.
	writer.WriteString(truncationMarker)

	// Now, read the tail block and process it.
	if _, err := src.Seek(tailReadStartOffset, io.SeekStart); err != nil {
		return err
	}

	// We read the entire tail block into memory. It's at most 50KB, which is
	// trivial. Anyone who complains about this is an idiot.
	tailBlock, err := io.ReadAll(src)
	if err != nil {
		return err
	}

	// The first line in our block might be a partial line. We find the first
	// newline and discard everything before it to ensure we only write complete lines.
	if idx := bytes.IndexByte(tailBlock, '\n'); idx >= 0 {
		tailBlock = tailBlock[idx+1:]
	}

	// Now, split into lines and enforce the line limit from the *end*.
	lines := bytes.Split(tailBlock, []byte{'\n'})
	if len(lines) > tailMaxLines {
		lines = lines[len(lines)-tailMaxLines:]
	}

	// Write the final, processed tail lines.
	for _, line := range lines {
		writer.Write(line)
		writer.WriteByte('\n')
	}

	return nil
}

// copyFile is a boring utility. Does what it says.
func copyFile(srcPath, dstPath string) error {
	src, err := os.Open(srcPath)
	if err != nil {
		return err
	}
	defer src.Close()

	dst, err := os.Create(dstPath)
	if err != nil {
		return err
	}
	defer dst.Close()

	_, err = io.Copy(dst, src)
	return err
}
