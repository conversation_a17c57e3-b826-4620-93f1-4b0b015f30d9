package api

import (
	"errors"
	"net/http"

	"github.com/caarlos0/httperr"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"go.uber.org/zap"

	// Import the generated swagger docs
	_ "github.com/breezewish/pantheon-backend/internal/docs"
)

// ErrorResponse represents an error response.
// @Description Error response schema
type ErrorResponse struct {
	Error string
}

func errHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()
		err := c.Errors.Last()
		if err == nil {
			return
		}
		zap.L().Error("API error", zap.Error(err))
		herr := httperr.Error{}
		if errors.As(err, &herr) {
			c.JSON(herr.Status, ErrorResponse{Error: herr.Error()})
		} else {
			c.<PERSON>(http.StatusInternalServerError, ErrorResponse{Error: "internal server error"})
		}
	}
}

// SetupRoutes sets up the API routes.
func SetupRoutes(r *gin.Engine, handler *Handler) {
	r.Use(errHandler())

	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// Swagger documentation endpoint
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	api := r.Group("/api/v1")
	forest := api.Group("/forest/:forestID")
	{
		snap := forest.Group("/snap/:snapID")
		{
			snap.POST("/exec", handler.ExecSnap)
			snap.GET("", handler.GetSnap)
			snap.GET("/artifacts/output", handler.GetSnapOutput)
			// snap.GET("/tree", handler.GetSnapTree)
			// snap.GET("/leaves", handler.GetSnapLeaves)
		}

		forest.POST("/burn", handler.BurnForest)
	}

	seedSnap := api.Group("/seed_snap")
	{
		seedSnap.POST("/import", handler.ImportSeedSnap)
	}
}
