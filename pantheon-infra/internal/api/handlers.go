package api

import (
	"net/http"

	"github.com/breezewish/pantheon-backend/internal/service"
	"github.com/gin-gonic/gin"
)

// Handler contains all HTTP handlers.
type Handler struct {
	snapService *service.SnapService
}

// NewHandler creates a new Handler.
func NewHandler(snapService *service.SnapService) *Handler {
	return &Handler{snapService: snapService}
}

// ExecSnap handles the execution of a command on a snap. This API does not wait for the execution
// to finish. You should pull the status of the execution separately by just GetSnap endpoint.
// @Summary Execute a command on a snap
// @Description Run a command in a snap environment
// @Tags snaps
// @Accept json
// @Produce json
// @Param forestID path string true "Forest ID"
// @Param snapID path string true "Snap ID"
// @Param execRequest body service.ExecRequest true "Execution request"
// @Success 200 {object} service.ExecResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /forest/{forestID}/snap/{snapID}/exec [post]
func (h *Handler) ExecSnap(c *gin.Context) {
	forestID := c.Param("forestID")
	snapID := c.Param("snapID")

	var req service.ExecRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(err)
		return
	}
	resp, err := h.snapService.Exec(forestID, snapID, req)
	if err != nil {
		c.Error(err)
		return
	}
	c.IndentedJSON(http.StatusOK, resp)
}

// GetSnap handles getting a snap by ID.
// @Summary Get a snap by ID
// @Description Get a snap by ID
// @Tags snaps
// @Produce json
// @Param forestID path string true "Forest ID"
// @Param snapID path string true "Snap ID"
// @Success 200 {object} service.GetSnapResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /forest/{forestID}/snap/{snapID} [get]
func (h *Handler) GetSnap(c *gin.Context) {
	forestID := c.Param("forestID")
	snapID := c.Param("snapID")

	snap, err := h.snapService.GetSnap(forestID, snapID)
	if err != nil {
		c.Error(err)
		return
	}
	c.IndentedJSON(http.StatusOK, snap)
}

// GetSnapOutput handles getting the output of a snap.
// @Summary Get the output of a snap
// @Description Get the command output of a snap
// @Tags snaps
// @Produce plain
// @Param forestID path string true "Forest ID"
// @Param snapID path string true "Snap ID"
// @Success 200 {string} string "Command output"
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /forest/{forestID}/snap/{snapID}/artifacts/output [get]
func (h *Handler) GetSnapOutput(c *gin.Context) {
	forestID := c.Param("forestID")
	snapID := c.Param("snapID")

	output, err := h.snapService.GetSnapOutput(forestID, snapID)
	if err != nil {
		c.Error(err)
		return
	}

	c.String(http.StatusOK, output)
}

// BurnForest handles burning a forest.
// @Summary Burn (delete) a forest
// @Description Delete a forest and all its snaps
// @Tags forests
// @Param forestID path string true "Forest ID"
// @Success 204 "No Content"
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /forest/{forestID}/burn [post]
func (h *Handler) BurnForest(c *gin.Context) {
	forestID := c.Param("forestID")

	if err := h.snapService.BurnForest(forestID); err != nil {
		c.Error(err)
		return
	}

	c.Status(http.StatusNoContent)
}

// ImportSeedSnap handles importing a seed snap.
// @Summary Import a seed snap
// @Description Import a seed snap as a starting point
// @Tags snaps
// @Accept json
// @Produce json
// @Param importSeedRequest body service.ImportSeedRequest true "Import seed request"
// @Success 201 {object} service.ImportSeedResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /seed_snap/import [post]
func (h *Handler) ImportSeedSnap(c *gin.Context) {
	var req service.ImportSeedRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(err)
		return
	}
	resp, err := h.snapService.ImportSeedSnap(req)
	if err != nil {
		c.Error(err)
		return
	}
	c.IndentedJSON(http.StatusCreated, resp)
}
