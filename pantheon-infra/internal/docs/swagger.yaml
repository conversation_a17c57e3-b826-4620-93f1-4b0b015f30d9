definitions:
  api.ErrorResponse:
    description: Error response schema
    properties:
      Error:
        type: string
    type: object
  service.ExecRequest:
    properties:
      Command:
        example:
        - /bin/bash
        - -c
        - echo hello
        items:
          type: string
        type: array
      Envs:
        additionalProperties:
          type: string
        example:
          DEBUG: "1"
        type: object
      NewSnapName:
        example: ""
        type: string
      WorkingDir:
        example: ""
        type: string
    type: object
  service.ExecResponse:
    properties:
      Snap:
        $ref: '#/definitions/service.SnapObject'
    type: object
  service.GetSnapResponse:
    properties:
      Snap:
        $ref: '#/definitions/service.SnapObject'
    type: object
  service.ImportSeedRequest:
    properties:
      DockerImage:
        example: busybox:1.37.0
        type: string
      Envs:
        additionalProperties:
          type: string
        example:
          DEBUG: "1"
        type: object
      ID:
        example: mybox
        type: string
    type: object
  service.ImportSeedResponse:
    properties:
      Snap:
        $ref: '#/definitions/service.SnapObject'
    type: object
  service.SnapObject:
    properties:
      BaseSnapID:
        type: string
      Command:
        items:
          type: string
        type: array
      Envs:
        additionalProperties:
          type: string
        type: object
      Err:
        type: string
      ExitCode:
        type: integer
      FinishedAt:
        type: integer
      ForestID:
        type: string
      ID:
        type: string
      Name:
        type: string
      StartedAt:
        type: integer
      Status:
        type: string
      WorkDir:
        type: string
    type: object
info:
  contact: {}
paths:
  /forest/{forestID}/burn:
    post:
      description: Delete a forest and all its snaps
      parameters:
      - description: Forest ID
        in: path
        name: forestID
        required: true
        type: string
      responses:
        "204":
          description: No Content
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: Burn (delete) a forest
      tags:
      - forests
  /forest/{forestID}/snap/{snapID}:
    get:
      description: Get a snap by ID
      parameters:
      - description: Forest ID
        in: path
        name: forestID
        required: true
        type: string
      - description: Snap ID
        in: path
        name: snapID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.GetSnapResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: Get a snap by ID
      tags:
      - snaps
  /forest/{forestID}/snap/{snapID}/artifacts/output:
    get:
      description: Get the command output of a snap
      parameters:
      - description: Forest ID
        in: path
        name: forestID
        required: true
        type: string
      - description: Snap ID
        in: path
        name: snapID
        required: true
        type: string
      produces:
      - text/plain
      responses:
        "200":
          description: Command output
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: Get the output of a snap
      tags:
      - snaps
  /forest/{forestID}/snap/{snapID}/exec:
    post:
      consumes:
      - application/json
      description: Run a command in a snap environment
      parameters:
      - description: Forest ID
        in: path
        name: forestID
        required: true
        type: string
      - description: Snap ID
        in: path
        name: snapID
        required: true
        type: string
      - description: Execution request
        in: body
        name: execRequest
        required: true
        schema:
          $ref: '#/definitions/service.ExecRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ExecResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: Execute a command on a snap
      tags:
      - snaps
  /seed_snap/import:
    post:
      consumes:
      - application/json
      description: Import a seed snap as a starting point
      parameters:
      - description: Import seed request
        in: body
        name: importSeedRequest
        required: true
        schema:
          $ref: '#/definitions/service.ImportSeedRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/service.ImportSeedResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: Import a seed snap
      tags:
      - snaps
swagger: "2.0"
