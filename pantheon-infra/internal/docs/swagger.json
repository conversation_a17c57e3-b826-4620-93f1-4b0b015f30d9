{"swagger": "2.0", "info": {"contact": {}}, "paths": {"/forest/{forestID}/burn": {"post": {"description": "Delete a forest and all its snaps", "tags": ["forests"], "summary": "Burn (delete) a forest", "parameters": [{"type": "string", "description": "Forest ID", "name": "forestID", "in": "path", "required": true}], "responses": {"204": {"description": "No Content"}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/forest/{forestID}/snap/{snapID}": {"get": {"description": "Get a snap by ID", "produces": ["application/json"], "tags": ["snaps"], "summary": "Get a snap by ID", "parameters": [{"type": "string", "description": "Forest ID", "name": "forestID", "in": "path", "required": true}, {"type": "string", "description": "Snap ID", "name": "snapID", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.GetSnapResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/forest/{forestID}/snap/{snapID}/artifacts/output": {"get": {"description": "Get the command output of a snap", "produces": ["text/plain"], "tags": ["snaps"], "summary": "Get the output of a snap", "parameters": [{"type": "string", "description": "Forest ID", "name": "forestID", "in": "path", "required": true}, {"type": "string", "description": "Snap ID", "name": "snapID", "in": "path", "required": true}], "responses": {"200": {"description": "Command output", "schema": {"type": "string"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/forest/{forestID}/snap/{snapID}/exec": {"post": {"description": "Run a command in a snap environment", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["snaps"], "summary": "Execute a command on a snap", "parameters": [{"type": "string", "description": "Forest ID", "name": "forestID", "in": "path", "required": true}, {"type": "string", "description": "Snap ID", "name": "snapID", "in": "path", "required": true}, {"description": "Execution request", "name": "execRequest", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.ExecRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.ExecResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/seed_snap/import": {"post": {"description": "Import a seed snap as a starting point", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["snaps"], "summary": "Import a seed snap", "parameters": [{"description": "Import seed request", "name": "importSeedRequest", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.ImportSeedRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/service.ImportSeedResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}}, "definitions": {"api.ErrorResponse": {"description": "Error response schema", "type": "object", "properties": {"Error": {"type": "string"}}}, "service.ExecRequest": {"type": "object", "properties": {"Command": {"type": "array", "items": {"type": "string"}, "example": ["/bin/bash", "-c", "echo hello"]}, "Envs": {"type": "object", "additionalProperties": {"type": "string"}, "example": {"DEBUG": "1"}}, "NewSnapName": {"type": "string", "example": ""}, "WorkingDir": {"type": "string", "example": ""}}}, "service.ExecResponse": {"type": "object", "properties": {"Snap": {"$ref": "#/definitions/service.SnapObject"}}}, "service.GetSnapResponse": {"type": "object", "properties": {"Snap": {"$ref": "#/definitions/service.SnapObject"}}}, "service.ImportSeedRequest": {"type": "object", "properties": {"DockerImage": {"type": "string", "example": "busybox:1.37.0"}, "Envs": {"type": "object", "additionalProperties": {"type": "string"}, "example": {"DEBUG": "1"}}, "ID": {"type": "string", "example": "mybox"}}}, "service.ImportSeedResponse": {"type": "object", "properties": {"Snap": {"$ref": "#/definitions/service.SnapObject"}}}, "service.SnapObject": {"type": "object", "properties": {"BaseSnapID": {"type": "string"}, "Command": {"type": "array", "items": {"type": "string"}}, "Envs": {"type": "object", "additionalProperties": {"type": "string"}}, "Err": {"type": "string"}, "ExitCode": {"type": "integer"}, "FinishedAt": {"type": "integer"}, "ForestID": {"type": "string"}, "ID": {"type": "string"}, "Name": {"type": "string"}, "StartedAt": {"type": "integer"}, "Status": {"type": "string"}, "WorkDir": {"type": "string"}}}}}