package local

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strconv"

	"github.com/docker/docker/client"
)

// The MIT License (MIT)
// Copyright (c) 2017-2019 Gian<PERSON>ca <PERSON>rb<PERSON>

// dockerHostCheck Use a vanilla Docker client to check if the Docker host is reachable.
// It will avoid recursive calls to this function.
var dockerHostCheck = func(ctx context.Context, host string) error {
	cli, err := client.NewClientWithOpts(client.FromEnv, client.WithHost(host), client.WithAPIVersionNegotiation())
	if err != nil {
		return fmt.Errorf("new client: %w", err)
	}
	defer cli.Close()
	_, err = cli.Info(ctx)
	if err != nil {
		return fmt.Errorf("docker info: %w", err)
	}
	return nil
}

// extractDockerHost Extracts the docker host from the different alternatives.
// The possible alternatives are:
//
//  1. DOCKER_HOST environment variable.
//  2. Docker host from the default docker socket path, without the unix schema.
//  3. Rootless docker socket path.
//  4. Else, because the Docker host is not set, it panics.
func extractDockerHost(ctx context.Context) (string, error) {
	dockerHostFns := []func(context.Context) (string, bool){
		dockerHostFromEnv,
		dockerSocketPath,
		rootlessDockerSocketPath,
	}
	var errs []error
	for _, dockerHostFn := range dockerHostFns {
		dockerHost, ok := dockerHostFn(ctx)
		if !ok {
			continue
		}
		if err := dockerHostCheck(ctx, dockerHost); err != nil {
			errs = append(errs, fmt.Errorf("check host %q: %w", dockerHost, err))
			continue
		}
		return dockerHost, nil
	}
	if len(errs) > 0 {
		return "", errors.Join(errs...)
	}
	return "", fmt.Errorf("no valid Docker host found")
}

// dockerHostFromEnv returns the docker host from the DOCKER_HOST environment variable, if it's not empty
func dockerHostFromEnv(_ context.Context) (string, bool) {
	if dockerHostPath := os.Getenv("DOCKER_HOST"); dockerHostPath != "" {
		return dockerHostPath, true
	}
	return "", false
}

// dockerSocketPath returns the docker socket from the default docker socket path, if it's not empty
// and the socket exists
func dockerSocketPath(_ context.Context) (string, bool) {
	if fileExists("/var/run/docker.sock") {
		return "unix:///var/run/docker.sock", true
	}
	return "", false
}

// rootlessDockerSocketPath returns if the path to the rootless Docker socket exists.
// The rootless socket path is determined by the following order:
//
//  1. XDG_RUNTIME_DIR environment variable.
//  2. ~/.docker/run/docker.sock file.
//  3. ~/.docker/desktop/docker.sock file.
//  4. /run/user/${uid}/docker.sock file.
//  5. Else, return ErrRootlessDockerNotFound, wrapping specific errors for each of the above paths.
//
// It should include the Docker socket schema (unix://) in the returned path.
func rootlessDockerSocketPath(_ context.Context) (string, bool) {
	socketPathFns := []func() (string, bool){
		rootlessSocketPathFromEnv,
		rootlessSocketPathFromHomeRunDir,
		rootlessSocketPathFromHomeDesktopDir,
		rootlessSocketPathFromRunDir,
	}
	for _, socketPathFn := range socketPathFns {
		s, ok := socketPathFn()
		if !ok {
			continue
		}
		return "unix://" + s, true
	}
	return "", false
}

// rootlessSocketPathFromEnv returns the path to the rootless Docker socket from the XDG_RUNTIME_DIR environment variable.
// It should include the Docker socket schema (unix://) in the returned path.
func rootlessSocketPathFromEnv() (string, bool) {
	xdgRuntimeDir, exists := os.LookupEnv("XDG_RUNTIME_DIR")
	if exists {
		f := filepath.Join(xdgRuntimeDir, "docker.sock")
		if fileExists(f) {
			return f, true
		}
	}
	return "", false
}

// rootlessSocketPathFromHomeRunDir returns the path to the rootless Docker socket from the ~/.docker/run/docker.sock file.
func rootlessSocketPathFromHomeRunDir() (string, bool) {
	home, err := os.UserHomeDir()
	if err != nil {
		return "", false
	}
	f := filepath.Join(home, ".docker", "run", "docker.sock")
	if fileExists(f) {
		return f, true
	}
	return "", false
}

// rootlessSocketPathFromHomeDesktopDir returns the path to the rootless Docker socket from the ~/.docker/desktop/docker.sock file.
func rootlessSocketPathFromHomeDesktopDir() (string, bool) {
	home, err := os.UserHomeDir()
	if err != nil {
		return "", false
	}
	f := filepath.Join(home, ".docker", "desktop", "docker.sock")
	if fileExists(f) {
		return f, true
	}
	return "", false
}

// rootlessSocketPathFromRunDir returns the path to the rootless Docker socket from the /run/user/<uid>/docker.sock file.
func rootlessSocketPathFromRunDir() (string, bool) {
	uid := os.Getuid()
	f := filepath.Join("/run", "user", strconv.Itoa(uid), "docker.sock")
	if fileExists(f) {
		return f, true
	}
	return "", false
}

func fileExists(f string) bool {
	_, err := os.Stat(f)
	return err == nil
}
