package service

import (
	"errors"
	"fmt"
	"net/http"
	"regexp"
	"time"

	"github.com/breezewish/pantheon-backend/internal/model"
	"github.com/breezewish/pantheon-backend/internal/provider"
	"github.com/caarlos0/httperr"
	"github.com/google/uuid"
	"github.com/guregu/null/v5"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

const FOREST_ID_SEED = "__seed__"

var forestIDRegex = regexp.MustCompile(`^[a-zA-Z0-9-_]{1,32}$`)
var seedIDRegex = regexp.MustCompile(`^[a-zA-Z0-9-_]{1,32}$`)

// SnapService manages snaps and their execution.
type SnapService struct {
	db       *gorm.DB
	provider provider.Provider
}

func wrapDBErr(resourceName string, err error) error {
	if err == nil {
		return nil
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return httperr.Errorf(http.StatusNotFound, "%s not found", resourceName)
	}
	if errors.Is(err, gorm.ErrDuplicatedKey) {
		return httperr.Errorf(http.StatusConflict, "%s already exists", resourceName)
	}
	return err
}

// NewSnapService creates a new SnapService instance.
func NewSnapService(db *gorm.DB, provider provider.Provider) *SnapService {
	return &SnapService{
		db:       db,
		provider: provider,
	}
}

// Exec executes a command on a snapshot and creates a new snapshot.
// Example:
//
//	curl -X POST -d '{"ID": "box", "Command": ["whoami"]}' http://localhost:8080/api/v1/forest/my_forest/snap/box/exec
func (s *SnapService) Exec(forestID string, snapID string, req ExecRequest) (*ExecResponse, error) {
	if forestID == FOREST_ID_SEED {
		return nil, httperr.Errorf(http.StatusBadRequest, "invalid forest ID")
	}
	if !forestIDRegex.MatchString(forestID) {
		return nil, httperr.Errorf(http.StatusBadRequest, "invalid forest ID")
	}

	// Find the source snap
	// The source snap may be either a snap in this forest, or a seed snap
	var sourceSnap model.Snap
	if err := wrapDBErr("snap",
		s.db.Where("id = ? AND (forest_id = ? OR forest_id = ?)", snapID, forestID, FOREST_ID_SEED).First(&sourceSnap).Error,
	); err != nil {
		return nil, err
	}

	// Ensure working directory is set
	workDir := req.WorkingDir
	if workDir == "" {
		workDir = "/"
	}

	// Generate a new snap ID
	newSnapID := s.generateSnapID(sourceSnap.BaseSnapID)

	// The new env is a combination of the source snap's envs and the request's envs
	newEnvs := make(map[string]string)
	for k, v := range sourceSnap.Envs {
		newEnvs[k] = v
	}
	for k, v := range req.Envs {
		newEnvs[k] = v
	}

	providerOutput, err := s.provider.AsyncExec(provider.AsyncExecOpts{
		SrcSnapToken: sourceSnap.SnapToken,
		Command:      req.Command,
		WorkDir:      workDir,
		Envs:         newEnvs,
	})
	if err != nil {
		return nil, httperr.Errorf(http.StatusBadGateway, "failed to execute command: %s", err.Error())
	}

	// Build ancestry path for the new snap
	ancestryPath := append([]string{}, sourceSnap.AncestryPath...)
	ancestryPath = append(ancestryPath, sourceSnap.ID)

	// Create a new snap record
	newSnap := &model.Snap{
		ID:           newSnapID,
		ForestID:     forestID,
		Name:         req.NewSnapName,
		SnapToken:    "",
		SourceSnapID: snapID,
		BaseSnapID:   sourceSnap.BaseSnapID,
		AncestryPath: ancestryPath,
		Command:      req.Command,
		WorkDir:      workDir,
		Envs:         newEnvs,
		Status:       model.SnapStatusRunning,
		StartedAt:    null.NewInt(time.Now().UnixNano(), true),
		FinishedAt:   null.Int{},
		ExitCode:     null.Int32{},
		Err:          null.String{},
		StdoutToken:  null.NewString(providerOutput.StdoutToken, true),
		StderrToken:  null.NewString(providerOutput.StderrToken, true),
	}

	if err := wrapDBErr("snap", s.db.Create(newSnap).Error); err != nil {
		return nil, err
	}

	go func() {
		execError := <-providerOutput.FinishCh
		if execError != nil {
			if err := s.db.Model(newSnap).Select("Status", "Err", "FinishedAt").Updates(model.Snap{
				Status:     model.SnapStatusFinished,
				Err:        null.NewString(execError.Error(), true),
				FinishedAt: null.NewInt(time.Now().UnixNano(), true),
			}).Error; err != nil {
				zap.L().Error("failed to update snap record", zap.Error(err))
			}
			return
		}
		if err := s.db.Model(newSnap).Select("Status", "FinishedAt", "ExitCode", "SnapToken").Updates(model.Snap{
			Status:     model.SnapStatusFinished,
			FinishedAt: null.NewInt(time.Now().UnixNano(), true),
			ExitCode:   null.NewInt32(0, true),
			SnapToken:  providerOutput.NewSnapToken.Load(),
		}).Error; err != nil {
			zap.L().Error("failed to update snap record", zap.Error(err))
		}
	}()

	return &ExecResponse{Snap: newSnapObject(newSnap)}, nil
}

// ImportSeedSnap imports a new seed snap.
// Example:
//
//	curl -i -X POST http://localhost:8080/api/v1/seed_snap/import -d '{"ID": "box", "DockerImage": "busybox:1.37.0"}'
func (s *SnapService) ImportSeedSnap(req ImportSeedRequest) (*ImportSeedResponse, error) {
	if !seedIDRegex.MatchString(req.ID) {
		return nil, httperr.Errorf(http.StatusBadRequest, "invalid seed snap ID")
	}

	var count int64
	s.db.Model(&model.Snap{}).Where("id = ?", req.ID).Count(&count)
	if count > 0 {
		return nil, httperr.Errorf(http.StatusConflict, "seed snap id already exists")
	}

	token, err := s.provider.Import(req.DockerImage)
	if err != nil {
		return nil, httperr.Errorf(http.StatusBadGateway, "failed to import seed snap: %s", err.Error())
	}

	// Create ancestry path for the seed snap
	ancestryPath := []string{req.ID}

	// Create a new snap record for the seed snap
	seedSnap := &model.Snap{
		ID:           req.ID,
		ForestID:     FOREST_ID_SEED,
		Name:         req.ID,
		SnapToken:    token,
		SourceSnapID: "",
		BaseSnapID:   req.ID,
		AncestryPath: ancestryPath,
		Command:      []string{},
		WorkDir:      "/",
		Envs:         req.Envs,
		Status:       model.SnapStatusFinished,
		StartedAt:    null.NewInt(time.Now().UnixNano(), true),
		FinishedAt:   null.NewInt(time.Now().UnixNano(), true),
		ExitCode:     null.Int32{},
		Err:          null.String{},
		StdoutToken:  null.String{},
		StderrToken:  null.String{},
	}

	// Save the seed snap record to database
	if err := wrapDBErr("seed snap", s.db.Create(seedSnap).Error); err != nil {
		return nil, err
	}

	return &ImportSeedResponse{Snap: newSnapObject(seedSnap)}, nil
}

// GetSnap returns a snap by its ID and forest ID.
// Example:
//
//	curl -i http://localhost:8080/api/v1/forest/my_forest/snap/box.7be56117
func (s *SnapService) GetSnap(forestID string, snapID string) (*GetSnapResponse, error) {
	var snap model.Snap
	if err := wrapDBErr("snap",
		s.db.Where("id = ? AND forest_id = ?", snapID, forestID).First(&snap).Error,
	); err != nil {
		return nil, err
	}
	return &GetSnapResponse{Snap: newSnapObject(&snap)}, nil
}

// GetSnapTree returns a tree of snaps starting from a given snap.
func (s *SnapService) GetSnapTree(forestID string, snapID string, maxDepth int) ([]model.Snap, error) {
	if forestID == FOREST_ID_SEED {
		return nil, httperr.Errorf(http.StatusBadRequest, "invalid forest ID")
	}

	return nil, fmt.Errorf("not implemented")

	// // Get the root snap first
	// var rootSnap model.Snap
	// result := s.db.Where("id = ? AND forest_id = ?", snapID, forestID).First(&rootSnap)
	// if result.Error != nil {
	// 	return nil, fmt.Errorf("snap not found")
	// }

	// // Find all snaps that have this snap in their ancestry path
	// var snaps []model.Snap
	// query := s.db.Where("forest_id = ? AND JSON_CONTAINS(ancestry_path, ?)", forestID, fmt.Sprintf(`"%s"`, snapID))

	// result = query.Find(&snaps)
	// if result.Error != nil {
	// 	return nil, fmt.Errorf("failed to fetch snap tree: %w", result.Error)
	// }

	// // Add the root snap to the result
	// snaps = append([]model.Snap{rootSnap}, snaps...)

	// return snaps, nil
}

// GetSnapLeaves returns all leaf snaps in a tree starting from a given snap.
func (s *SnapService) GetSnapLeaves(forestID string, snapID string) ([]model.Snap, error) {
	if forestID == FOREST_ID_SEED {
		return nil, httperr.Errorf(http.StatusBadRequest, "invalid forest ID")
	}

	return nil, fmt.Errorf("not implemented")

	// // Get all snaps that have this snap in their ancestry path
	// snaps, err := s.GetSnapTree(forestID, snapID, 0)
	// if err != nil {
	// 	return nil, err
	// }

	// // Find all snaps that are not sources for any other snap
	// var leaves []model.Snap
	// for _, snap := range snaps {
	// 	var count int64
	// 	s.db.Model(&model.Snap{}).Where("forest_id = ? AND source_snap_id = ?", forestID, snap.ID).Count(&count)
	// 	if count == 0 {
	// 		leaves = append(leaves, snap)
	// 	}
	// }

	// return leaves, nil
}

// GetSnapOutput returns the output of a snap.
func (s *SnapService) GetSnapOutput(forestID string, snapID string) (string, error) {
	if forestID == FOREST_ID_SEED {
		return "", httperr.Errorf(http.StatusBadRequest, "invalid forest ID")
	}

	// Get the snap first
	var snap model.Snap
	if err := wrapDBErr("snap",
		s.db.Where("id = ? AND forest_id = ?", snapID, forestID).First(&snap).Error,
	); err != nil {
		return "", err
	}

	// Check if the snap has stdout token
	if !snap.StdoutToken.Valid || snap.StdoutToken.String == "" {
		return "", httperr.Errorf(http.StatusNoContent, "snap does not have output")
	}

	// Get the stdout from the provider
	output, err := s.provider.GetStdout(snap.StdoutToken.String)
	if err != nil {
		return "", httperr.Errorf(http.StatusBadGateway, "failed to get snap output: %s", err.Error())
	}

	return output, nil
}

// BurnForest removes all snaps in a forest.
func (s *SnapService) BurnForest(forestID string) error {
	if forestID == FOREST_ID_SEED {
		return httperr.Errorf(http.StatusBadRequest, "invalid forest ID")
	}

	return fmt.Errorf("not implemented")
	// if strings.HasPrefix(forestID, internalForestPrefix) {
	// 	return fmt.Errorf("invalid forest ID")
	// }

	// // Find all snaps in the forest
	// var snaps []model.Snap
	// result := s.db.Where("forest_id = ?", forestID).Find(&snaps)
	// if result.Error != nil {
	// 	return fmt.Errorf("failed to fetch forest snaps: %w", result.Error)
	// }

	// // Delete all snaps in the forest
	// for _, snap := range snaps {
	// 	// Delete output file if it exists
	// 	if snap.OutputPath != "" {
	// 		os.Remove(snap.OutputPath)
	// 	}

	// 	// Delete snap from database
	// 	if err := s.db.Delete(&snap).Error; err != nil {
	// 		return fmt.Errorf("failed to delete snap %s: %w", snap.ID, err)
	// 	}

	// 	// Clean up provider resources
	// 	if err := s.provider.Cleanup(snap.SnapToken); err != nil {
	// 		// Log error but continue
	// 		fmt.Printf("failed to cleanup provider resources for snap %s: %s\n", snap.ID, err)
	// 	}
	// }

	// return nil
}

// generateSnapID generates a new snap ID based on the base snap ID.
func (s *SnapService) generateSnapID(baseSnapID string) string {
	shortUUID := uuid.New().String()[:8]
	return fmt.Sprintf("%s.%s", baseSnapID, shortUUID)
}
