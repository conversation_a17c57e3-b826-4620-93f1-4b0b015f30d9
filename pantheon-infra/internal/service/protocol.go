package service

import (
	"github.com/breezewish/pantheon-backend/internal/model"
	"github.com/guregu/null/v5"
)

type SnapObject struct {
	ID         string
	ForestID   string
	Name       string
	BaseSnapID string
	Command    []string
	WorkDir    string
	Envs       map[string]string
	Status     string
	StartedAt  null.Int64  `swaggertype:"integer"`
	FinishedAt null.Int64  `swaggertype:"integer"`
	ExitCode   null.Int32  `swaggertype:"integer"`
	Err        null.String `swaggertype:"string"`
}

func newSnapObject(snap *model.Snap) SnapObject {
	return SnapObject{
		ID:         snap.ID,
		ForestID:   snap.ForestID,
		Name:       snap.Name,
		BaseSnapID: snap.BaseSnapID,
		Command:    snap.Command,
		WorkDir:    snap.WorkDir,
		Envs:       snap.Envs,
		Status:     snap.Status,
		StartedAt:  snap.StartedAt,
		FinishedAt: snap.FinishedAt,
		ExitCode:   snap.ExitCode,
		Err:        snap.Err,
	}
}

// ExecResponse represents the output of an exec operation.
type ExecResponse struct {
	Snap SnapObject
}

// ExecRequest represents the input for an exec operation.
type ExecRequest struct {
	Command     []string          `example:"/bin/bash,-c,echo hello"`
	WorkingDir  string            `example:""`
	NewSnapName string            `example:""`
	Envs        map[string]string `example:"DEBUG:1"`
}

// ImportSeedResponse represents the output of an import seed operation.
type ImportSeedResponse struct {
	Snap SnapObject
}

// ImportSeedRequest represents the input for an import seed operation.
type ImportSeedRequest struct {
	ID          string            `example:"mybox"`
	Envs        map[string]string `example:"DEBUG:1"`
	DockerImage string            `example:"busybox:1.37.0"`
}

// GetSnapResponse represents the output of a get snap operation.
type GetSnapResponse struct {
	Snap SnapObject
}
