package model

import (
	"github.com/guregu/null/v5"
)

// Snap represents a snapshot in the system.
type Snap struct {
	ID       string `gorm:"primaryKey;type:varchar(64)"` // e.g., "my-base" or "my-base/a1b2c3d4"
	ForestID string `gorm:"index;type:varchar(64)"`
	Name     string `gorm:"type:varchar(255)"` // User-defined newSnapName

	SnapToken string `gorm:"type:varchar(255)"` // The real ID that provider cares, e.g., Docker image hash sha256:...

	SourceSnapID string `gorm:"type:varchar(64)"`       // The immediate parent's ID
	BaseSnapID   string `gorm:"index;type:varchar(64)"` // The seed snap ID, e.g., "my-base"

	// JSON array of ancestor IDs. Solves the tree query problem efficiently.
	// e.g., ["base-id", "base-id/snap1", "base-id/snap2"]
	AncestryPath []string `gorm:"serializer:json"`

	Command []string          `gorm:"serializer:json"`
	WorkDir string            `gorm:"type:longtext"`
	Envs    map[string]string `gorm:"serializer:json"` // Store the envs used for this snap execution.

	Status    string `gorm:"type:varchar(20)"` // PENDING, RUNNING, FINISHED, FAILED
	StartedAt null.Int

	FinishedAt  null.Int
	ExitCode    null.Int32
	Err         null.String `gorm:"type:text"`         // Error message if any
	StdoutToken null.String `gorm:"type:varchar(255)"` // Seed Snaps does not have this
	StderrToken null.String `gorm:"type:varchar(255)"` // Seed Snaps does not have this
}

// Constant values for Snap.Status
const (
	SnapStatusRunning  = "RUNNING"
	SnapStatusFinished = "FINISHED"
)

// TableName returns the table name for Snap.
func (Snap) TableName() string {
	return "snaps"
}
