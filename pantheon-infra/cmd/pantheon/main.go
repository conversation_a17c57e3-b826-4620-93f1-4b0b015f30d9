package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/breezewish/pantheon-backend/internal/app"
	"github.com/breezewish/pantheon-backend/internal/config"
)

func main() {
	// Execute root command
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

func runServer(cfg *config.Config) {
	// Create and start the application
	fxApp := app.NewFxApp(cfg)
	startCtx := context.Background()
	
	if err := fxApp.Start(startCtx); err != nil {
		fmt.Printf("Failed to start application: %v\n", err)
		os.Exit(1)
	}

	// Wait for shutdown signal
	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)
	<-c

	// Gracefully shutdown the application
	stopCtx, cancel := context.WithTimeout(context.Background(), app.DefaultShutdownTimeout)
	defer cancel()
	
	if err := fxApp.Stop(stopCtx); err != nil {
		fmt.Printf("Failed to stop application gracefully: %v\n", err)
		os.Exit(1)
	}
}