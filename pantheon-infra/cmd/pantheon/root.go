package main

import (
	"fmt"
	"os"
	"strconv"

	"github.com/breezewish/pantheon-backend/internal/config"
	"github.com/spf13/cobra"
)

var (
	cfgHTTPPort    int
	cfgHTTPHost    string
	cfgDatabaseDSN string
	cfgDataDir     string
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "pantheon",
	Short: "Pantheon backend service",
	Long:  `Pantheon provides a snapshot-based command execution service.`,
	Run: func(cmd *cobra.Command, args []string) {
		cfg := &config.Config{
			HTTP: config.HTTPConfig{
				Port: cfgHTTPPort,
				Host: cfgHTTPHost,
			},
			Database: config.DatabaseConfig{
				DSN: cfgDatabaseDSN,
			},
			Storage: config.StorageConfig{
				DataDir: cfgDataDir,
			},
		}

		// Start the server
		runServer(cfg)
	},
}

// Execute adds all child commands to the root command and sets flags appropriately.
func Execute() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

func init() {
	// Initialize configuration with default values
	defaultConfig := config.NewConfig()

	// Override defaults with environment variables if they exist
	if envPort := os.Getenv("PORT"); envPort != "" {
		if port, err := strconv.Atoi(envPort); err == nil {
			defaultConfig.HTTP.Port = port
		}
	}
	if envHost := os.Getenv("HOST"); envHost != "" {
		defaultConfig.HTTP.Host = envHost
	}
	if envDatabaseURL := os.Getenv("DATABASE_URL"); envDatabaseURL != "" {
		defaultConfig.Database.DSN = envDatabaseURL
	}
	if envDataDir := os.Getenv("DATA_DIR"); envDataDir != "" {
		defaultConfig.Storage.DataDir = envDataDir
	}

	// Define flags
	rootCmd.Flags().IntVar(&cfgHTTPPort, "port", defaultConfig.HTTP.Port, "HTTP server port")
	rootCmd.Flags().StringVar(&cfgHTTPHost, "host", defaultConfig.HTTP.Host, "HTTP server host")
	rootCmd.Flags().StringVar(&cfgDatabaseDSN, "db", defaultConfig.Database.DSN, "Database connection string")
	rootCmd.Flags().StringVar(&cfgDataDir, "data-dir", defaultConfig.Storage.DataDir, "Data directory path")
}
