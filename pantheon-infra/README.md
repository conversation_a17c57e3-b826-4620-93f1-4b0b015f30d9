# Pantheon Infra Backend

Pantheon Infra provides snapshot-based command execution capabilities.

- Local provider: Based on Docker to create snapshots and run commands
- JFS provider: Based on K8s and JFS to manage snapshots and execute commands

## Run Local Provider

Prerequisites: Docker

```bash
docker compose up --build
```

Full API documentations are available at http://localhost:8080/swagger/index.html

## Example

```plain
❯ curl -X POST -d '{"ID": "box", "DockerImage": "busybox:1.37.0"}' http://localhost:8080/api/v1/seed_snap/import
{
    "Snap": {
        "ID": "box",
        "ForestID": "__seed__",
        "Name": "box",
        "BaseSnapID": "box",
        "Command": [],
        "WorkDir": "/",
        "Envs": null,
        "Status": "FINISHED",
        "StartedAt": 1755492584112832125,
        "FinishedAt": 1755492584112832250,
        "ExitCode": null,
        "Err": null
    }
}


❯ curl -X POST -d '{"ID": "box", "Command": ["whoami"]}' http://localhost:8080/api/v1/forest/my_forest/snap/box/exec
{
    "Snap": {
        "ID": "box.b810139e",
        "ForestID": "my_forest",
        "Name": "",
        "BaseSnapID": "box",
        "Command": [
            "whoami"
        ],
        "WorkDir": "/",
        "Envs": {},
        "Status": "RUNNING",
        "StartedAt": 1755492596670618881,
        "FinishedAt": null,
        "ExitCode": null,
        "Err": null
    }
}


❯ curl http://localhost:8080/api/v1/forest/my_forest/snap/box.b810139e
{
    "Snap": {
        "ID": "box.b810139e",
        "ForestID": "my_forest",
        "Name": "",
        "BaseSnapID": "box",
        "Command": [
            "whoami"
        ],
        "WorkDir": "/",
        "Envs": {},
        "Status": "FINISHED",
        "StartedAt": 1755492596670618881,
        "FinishedAt": 1755492596735339882,
        "ExitCode": 0,
        "Err": null
    }
}


❯ curl http://localhost:8080/api/v1/forest/my_forest/snap/box.b810139e/artifacts/output
root
```
